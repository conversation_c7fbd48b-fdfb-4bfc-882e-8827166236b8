หากฉันอยากเพิ่ม Role : Account Management สำหรับมีสิทธิ์ เพิ่ม ลบ แก้ไข โครงการทุกโครงการในทีมที่ตัวเองอยู่ไม่ว่าจะอยู่มากว่า 1 สังกัดหรือทีม หาก Login ด้วย  Role : Account Management ให้สามารถบริหารจัดการโครงการ หน้า Project.php ได้ จะต้องกระทบหน้าไหนบ้าง รวมถึงหน้าเพิ่ม ลบ แก้ไข ผู้ใช้งานระบบ ด้วยหรือไม่ และหน้าอื่นๆมีหน้าไหนบ้าง 

# สิทธิ์และบทบาทในระบบ Sales Management

## 📋 ภาพรวมบทบาท (Roles Overview)

ระบบมีบทบาทหลัก 5 ประเภท แต่ละบทบาทมีสิทธิ์การเข้าถึงข้อมูลและฟังก์ชันที่แตกต่างกัน

| บทบาท | ระดับสิทธิ์ | คำอธิบาย |
|--------|------------|----------|
| 👑 **Executive** | สูงสุด | ผู้บริหารระดับสูง เข้าถึงข้อมูลทั้งหมด |
| 💼 **Account Management** | สูง | จัดการโครงการทุกโครงการในทีมที่สังกัด |
| 🎯 **Sale Supervisor** | กลาง-สูง | หัวหน้าทีมขาย จัดการทีมและสมาชิก |
| 💼 **Seller** | กลาง | พนักงานขาย ทำงานกับโครงการของตัวเอง |
| 🔧 **Engineer** | กลาง-จำกัด | วิศวกร เน้นงานเทคนิค ไม่เห็นข้อมูลการเงิน |

---

## 🏗️ โมดูลโครงการ (Project Module)

### 📊 สิทธิ์การเข้าถึงโครงการ

| บทบาท                     | ดูโครงการ      | เพิ่ม | แก้ไข            | ลบ            | จัดการสมาชิก |

| **Executive**             | ทั้งหมด         | ✅ | ทั้งหมด          | ทั้งหมด         | ทั้งหมด |
| **Account Management**    | ทีมที่สังกัด       | ✅ | ทีมที่สังกัด       | ทีมที่สังกัด       | ทีมที่สังกัด |
| **Sale Supervisor**       | ทีมที่สังกัด       | ✅ | ทีมที่สังกัด       | เฉพาะที่เป็นเจ้าของ | ทีมที่สังกัด |
| **Seller**                | ของตัวเอง + แชร์ | ✅ | ของตัวเอง + แชร์  | ของตัวเอง       | ของตัวเอง + แชร์ |
| **Engineer**              | ของตัวเอง + แชร์ | ❌ | ❌             | ❌             | ของตัวเอง + แชร์ |

### 🔍 รายละเอียดสิทธิ์โครงการ

#### **Executive (ผู้บริหาร)**
- **การเข้าถึง**: เห็นโครงการทั้งหมดในระบบ
- **การจัดการ**: สามารถเพิ่ม/แก้ไข/ลบโครงการใดก็ได้
- **ข้อมูลการเงิน**: เห็นทั้งหมด (ราคาขาย, ต้นทุน, กำไร)
- **การแชร์**: สามารถแชร์โครงการให้ผู้อื่นได้

#### **Account Management (ผู้จัดการบัญชี)** 🆕
- **การเข้าถึง**: เห็นโครงการทั้งหมดในทีมที่ตัวเองสังกัด
- **การจัดการ**: เพิ่ม/แก้ไข/ลบโครงการในทีมได้ทั้งหมด
- **ข้อมูลการเงิน**: เห็นทั้งหมดในทีมที่สังกัด
- **การแชร์**: สามารถแชร์โครงการในทีมได้
- **หลายทีม**: หากสังกัดหลายทีม จะเห็นโครงการของทุกทีม

#### **Sale Supervisor (หัวหน้าทีมขาย)**
- **การเข้าถึง**: เห็นโครงการของทีมที่ตัวเองดูแล
- **การจัดการ**: แก้ไขโครงการในทีมได้ แต่ลบได้เฉพาะที่ตัวเองเป็นเจ้าของ
- **ข้อมูลการเงิน**: เห็นข้อมูลการเงินของทีม
- **การแชร์**: สามารถแชร์โครงการในทีมได้

#### **Seller (พนักงานขาย)**
- **การเข้าถึง**: เห็นโครงการของตัวเอง + โครงการที่ถูกแชร์
- **การจัดการ**: เพิ่ม/แก้ไข/ลบโครงการของตัวเองได้
- **ข้อมูลการเงิน**: เห็นข้อมูลการเงินของโครงการตัวเอง
- **การแชร์**: สามารถแชร์โครงการของตัวเองได้

#### **Engineer (วิศวกร)**
- **การเข้าถึง**: เห็นโครงการที่ตัวเองเกี่ยวข้อง + โครงการที่ถูกแชร์
- **การจัดการ**: ไม่สามารถเพิ่ม/แก้ไข/ลบโครงการได้
- **ข้อมูลการเงิน**: ❌ ไม่เห็นข้อมูลการเงิน (ราคา, ต้นทุน, กำไร)
- **การแชร์**: สามารถดูโครงการที่ถูกแชร์ได้

---

## 👥 โมดูลจัดการผู้ใช้งาน (Account Module)

### 🔐 สิทธิ์การจัดการผู้ใช้งาน

| บทบาท | ดูรายชื่อ | เพิ่มผู้ใช้ | แก้ไขผู้ใช้ | ลบผู้ใช้ |
|--------|----------|-----------|------------|----------|
| **Executive** | ทั้งหมด | ทุก Role | ทั้งหมด | ทั้งหมด |
| **Account Management** | ทีมที่สังกัด | Sale Supervisor, Seller, Engineer | ทีมที่สังกัด | ทีมที่สังกัด |
| **Sale Supervisor** | ทีมที่สังกัด | Seller, Engineer | ทีมที่สังกัด | ทีมที่สังกัด |
| **Seller** | ตัวเองเท่านั้น | ❌ | ตัวเองเท่านั้น | ❌ |
| **Engineer** | ตัวเองเท่านั้น | ❌ | ตัวเองเท่านั้น | ❌ |

### 📝 รายละเอียดการจัดการผู้ใช้งาน

#### **การเพิ่มผู้ใช้งาน**
- **Executive**: สร้างได้ทุก Role รวมถึง Executive อื่น
- **Account Management**: สร้างได้เฉพาะ Sale Supervisor, Seller และ Engineer ในทีมตัวเอง
- **Sale Supervisor**: สร้างได้เฉพาะ Seller และ Engineer ในทีมตัวเอง
- **Seller/Engineer**: ไม่สามารถสร้างผู้ใช้งานใหม่ได้

#### **การแก้ไขผู้ใช้งาน**
- **Executive**: แก้ไขได้ทุกบัญชี
- **Account Management**: แก้ไขได้เฉพาะผู้ใช้ในทีมที่สังกัด (ไม่รวม Executive)
- **Sale Supervisor**: แก้ไขได้เฉพาะผู้ใช้ในทีมตัวเอง (ไม่รวม Executive)
- **Seller/Engineer**: แก้ไขได้เฉพาะบัญชีตัวเอง

---

## 🏢 การจัดการทีม (Team Management)

### 👥 สิทธิ์การเข้าถึงข้อมูลทีม

| บทบาท | ดูข้อมูลทีม | สร้างทีม | แก้ไขทีม | ลบทีม | จัดการสมาชิก |
|--------|------------|----------|----------|--------|--------------|
| **Executive** | ทั้งหมด | ✅ | ทั้งหมด | ทั้งหมด | ทั้งหมด |
| **Account Management** | ทีมที่สังกัด | ❌ | ทีมที่สังกัด | ❌ | ทีมที่สังกัด |
| **Sale Supervisor** | ทีมที่สังกัด | ❌ | ทีมตัวเอง | ❌ | ทีมตัวเอง |
| **Seller** | ทีมตัวเอง | ❌ | ❌ | ❌ | ❌ |
| **Engineer** | ทีมตัวเอง | ❌ | ❌ | ❌ | ❌ |

---

## 📊 การเข้าถึงข้อมูลการเงิน

### 💰 สิทธิ์ดูข้อมูลการเงิน

| บทบาท | ราคาขาย | ต้นทุน | กำไร | รายงานการเงิน |
|--------|---------|--------|------|---------------|
| **Executive** | ✅ ทั้งหมด | ✅ ทั้งหมด | ✅ ทั้งหมด | ✅ ทั้งหมด |
| **Account Management** | ✅ ทีมที่สังกัด | ✅ ทีมที่สังกัด | ✅ ทีมที่สังกัด | ✅ ทีมที่สังกัด |
| **Sale Supervisor** | ✅ ทีมตัวเอง | ✅ ทีมตัวเอง | ✅ ทีมตัวเอง | ✅ ทีมตัวเอง |
| **Seller** | ✅ โครงการตัวเอง | ✅ โครงการตัวเอง | ✅ โครงการตัวเอง | ✅ โครงการตัวเอง |
| **Engineer** | ❌ | ❌ | ❌ | ❌ |

---

## 🔄 การแชร์โครงการ (Project Sharing)

### 📤 ระดับการแชร์

| ระดับ | คำอธิบาย | สิทธิ์ที่ได้รับ |
|-------|----------|----------------|
| **Full Access** | เข้าถึงเต็มรูปแบบ | ดู/แก้ไข/ลบ/จัดการสมาชิก |
| **Half Access** | เข้าถึงบางส่วน | ดู/จัดการสมาชิก (ไม่สามารถแก้ไข/ลบ) |
| **View Only** | ดูอย่างเดียว | ดูข้อมูลเท่านั้น |

### 🎯 สิทธิ์การแชร์ตาม Role

| บทบาท | สามารถแชร์ได้ | ระดับที่แชร์ได้ |
|--------|---------------|----------------|
| **Executive** | โครงการทั้งหมด | Full/Half/View |
| **Account Management** | โครงการทั้งหมด | Full/Half/View |
| **Sale Supervisor** โครงการทั้งหมด | Full/Half/View |
| **Seller** โครงการทั้งหมด | Full/Half/View |
| **Engineer** | ❌ | ❌ |

---

## 📁 ไฟล์ที่ได้รับผลกระทบจากการเพิ่ม Account Management

### 🔴 ไฟล์ที่ต้องแก้ไขเร่งด่วน (สำคัญมาก)

1. **`pages/project/project.php`** - หน้าหลักจัดการโครงการ
2. **`pages/project/add_project.php`** - เพิ่มโครงการใหม่
3. **`pages/project/edit_project.php`** - แก้ไขโครงการ
4. **`pages/project/delete_project.php`** - ลบโครงการ
5. **`pages/project/view_project.php`** - ดูรายละเอียดโครงการ

### 🟡 ไฟล์ที่ต้องแก้ไขปานกลาง

6. **`pages/account/account.php`** - จัดการผู้ใช้งาน
7. **`pages/account/add_user.php`** - เพิ่มผู้ใช้งาน
8. **`pages/account/edit_account.php`** - แก้ไขผู้ใช้งาน
9. **`include/navbar.php`** - เมนูนำทาง
10. **`index.php`** - หน้าแดชบอร์ด

### 🟢 ไฟล์ที่อาจต้องแก้ไข (ตามความจำเป็น)

11. **`pages/project/management/*.php`** - จัดการโครงการขั้นสูง
12. **`pages/project/project_member/*.php`** - จัดการสมาชิกโครงการ
13. **`pages/project/discussion/*.php`** - การสนทนาในโครงการ

---

## 🚀 ขั้นตอนการเพิ่ม Account Management

### 1. แก้ไขไฟล์หลัก
- เพิ่มเงื่อนไข role checking ในทุกไฟล์โครงการ
- ปรับปรุงการ query ข้อมูลให้รองรับ Account Management

### 2. ปรับปรุงการจัดการผู้ใช้งาน
- เพิ่มสิทธิ์ในการจัดการผู้ใช้งานในทีม
- ปรับปรุงการตรวจสอบสิทธิ์

### 3. ทดสอบระบบ
- ทดสอบการเข้าถึงข้อมูลโครงการ
- ทดสอบการจัดการผู้ใช้งาน
- ทดสอบการแชร์โครงการ

---

*อัปเดตล่าสุด: วันที่ 9 ตุลาคม 2025*
